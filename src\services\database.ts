import { Prisma, PrismaClient } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import z from 'zod';
import { ApprovalInstance, ApprovalStatus } from '../types/approval.js';
import {
  BrandFinancialDetail,
  BrandFinancialSummary,
  BrandQueryParams,
  CreateBrandRequest,
  CreateProjectRequest,
  CreateProjectRevenueRequest,
  CreateSupplierRequest,
  CreateWeeklyBudgetRequest,
  FinancialReportQueryParams,
  FinancialReportResponse,
  Project,
  ProjectQueryParams,
  ProjectRevenue,
  ProjectRevenueQueryParams,
  ProjectStats,
  ProjectStatus,
  RevenueStats,
  ServiceType,
  Supplier,
  SupplierQueryParams,
  SupplierStats,
  TaxRate,
  UpdateBrandRequest,
  UpdateProjectRequest,
  UpdateProjectRevenueRequest,
  UpdateSupplierRequest,
  UpdateWeeklyBudgetRequest,
  User,
  WeeklyBudget,
  WeeklyBudgetQueryParams,
  WeeklyBudgetStats,
  WeeklyBudgetStatus
} from '../types/project.js';

const createSupplierSchema = z.object({
    name: z.string().min(1, '供应商名称不能为空').max(200, '供应商名称不能超过200字符'),
    shortName: z.string().max(100, '简称不能超过100字符').optional(),
    code: z.string().max(50, '供应商编码不能超过50字符').optional(),
    contactPerson: z.string().max(100, '联系人不能超过100字符').optional(),
    contactPhone: z.string().max(20, '联系电话不能超过20字符').optional(),
    contactEmail: z.string().email('邮箱格式不正确').max(100, '邮箱不能超过100字符').optional(),
    address: z.string().optional(),
    taxNumber: z.string().max(50, '税号不能超过50字符').optional(),
    bankAccount: z.string().max(50, '银行账号不能超过50字符').optional(),
    bankName: z.string().max(200, '开户银行不能超过200字符').optional(),
    legalPerson: z.string().max(100, '法人代表不能超过100字符').optional(),
    serviceTypes: z.array(z.nativeEnum(ServiceType)).min(1, '至少选择一种服务类型'),
    preferredTaxRate: z.nativeEnum(TaxRate).optional(),
    creditLimit: z.number().min(0, '信用额度不能为负数').optional(),
    paymentTerms: z.string().optional(),
    rating: z.number().min(1).max(5, '评级必须在1-5之间').optional(),
    notes: z.string().optional(),
})


export class DatabaseService {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient({
      log: ['query', 'info', 'warn', 'error'],
    });
  }

  // Public getter method for permission management services
  get client(): PrismaClient {
    return this.prisma;
  }

  // 项目管理
  async createProject(data: CreateProjectRequest, createdBy: string) {
    const projectData: Prisma.ProjectCreateInput = {
      documentType: data.documentType.toUpperCase() as any,
      projectName: data.projectName,
      startDate: new Date(data.period.startDate),
      endDate: new Date(data.period.endDate),
      planningBudget: new Decimal(data.budget.planningBudget),
      influencerBudget: new Decimal(data.budget.influencerBudget),
      adBudget: new Decimal(data.budget.adBudget),
      otherBudget: new Decimal(data.budget.otherBudget),
      influencerCost: new Decimal(data.cost.influencerCost),
      adCost: new Decimal(data.cost.adCost),
      otherCost: new Decimal(data.cost.otherCost),
      estimatedInfluencerRebate: new Decimal(data.cost.estimatedInfluencerRebate),
      executorPM: data.executorPM,
      contentMediaIds: data.contentMediaIds,
      contractType: data.contractType.toUpperCase() as any,
      contractSigningStatus: (data.contractSigningStatus || 'PENDING').toUpperCase() as any,
      settlementRules: data.settlementRules,
      kpi: data.kpi,
      expectedPaymentMonth: data.expectedPaymentMonth,
      paymentTermDays: data.paymentTermDays,
      createdBy,
      updatedBy: createdBy,
      brand: {
        connect: { id: data.brandId }
      }
    };

    const project = await this.prisma.project.create({
      data: projectData,
      include: {
        brand: true,
        attachments: true,
      },
    });

    // 如果提供了附件ID，将附件关联到项目
    if (data.attachmentIds && data.attachmentIds.length > 0) {
      await this.prisma.attachment.updateMany({
        where: {
          id: {
            in: data.attachmentIds
          }
        },
        data: {
          projectId: project.id
        }
      });
    }

    // 重新获取项目数据（包含关联的附件）
    const projectWithAttachments = await this.prisma.project.findUnique({
      where: { id: project.id },
      include: {
        brand: true,
        attachments: true,
      },
    });

    return this.transformProject(projectWithAttachments!);
  }

  async getProjects(params: ProjectQueryParams = {}) {
    const {
      page = 1,
      pageSize = 20,
      documentType,
      brandId,
      contractType,
      contractSigningStatus,
      executorPM,
      status,
      keyword,
      startDate,
      endDate,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = params;

    const skip = (page - 1) * pageSize;
    const take = pageSize;

    // 构建查询条件
    const where: Prisma.ProjectWhereInput = {};

    if (documentType) where.documentType = documentType.toUpperCase() as any;
    if (brandId) where.brandId = brandId;
    if (contractType) where.contractType = contractType.toUpperCase() as any;
    if (contractSigningStatus) where.contractSigningStatus = contractSigningStatus.toUpperCase() as any;
    if (executorPM) where.executorPM = executorPM;
    if (status) where.status = status.toUpperCase() as any;
    if (keyword) {
      where.projectName = {
        contains: keyword,
        mode: 'insensitive'
      };
    }
    if (startDate) where.startDate = { gte: new Date(startDate) };
    if (endDate) where.endDate = { lte: new Date(endDate) };

    // 构建排序条件
    const orderBy: Prisma.ProjectOrderByWithRelationInput = {};
    if (sortBy === 'projectName') {
      orderBy.projectName = sortOrder;
    } else if (sortBy === 'profit') {
      // 按利润排序需要使用原始查询
      orderBy.planningBudget = sortOrder; // 临时使用预算排序
    } else {
      orderBy[sortBy as keyof Prisma.ProjectOrderByWithRelationInput] = sortOrder;
    }

    const [projects, total] = await Promise.all([
      this.prisma.project.findMany({
        skip,
        take,
        where,
        orderBy,
        include: {
          brand: true,
          attachments: true,
        },
      }),
      this.prisma.project.count({ where }),
    ]);

    // 转换项目数据并填充用户信息
    const transformedProjects = await Promise.all(
      projects.map(async (project) => {
        const transformedProject = this.transformProject(project);

        // 填充用户信息
        try {
          transformedProject.executorPMInfo = await this.getUserInfo(project.executorPM);
          transformedProject.contentMediaInfo = await Promise.all(
            project.contentMediaIds.map(id => this.getUserInfo(id))
          );
        } catch (error) {
          console.warn(`获取项目 ${project.id} 的用户信息失败:`, error);
          // 设置默认值以避免字段缺失
          transformedProject.executorPMInfo = {
            userid: project.executorPM,
            name: `用户${project.executorPM}`,
            department: '未知部门'
          };
          transformedProject.contentMediaInfo = project.contentMediaIds.map(id => ({
            userid: id,
            name: `用户${id}`,
            department: '未知部门'
          }));
        }

        return transformedProject;
      })
    );

    return {
      projects: transformedProjects,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    };
  }
  
  async getProjectByName(projectName: string) {
    const project = await this.prisma.project.findFirst({
      where: { projectName },
      include: {
        brand: true,
        attachments: true,
      },
    });

    if (!project) {
      return null;
    }

    const transformedProject = this.transformProject(project);

    // 填充用户信息
    try {
      transformedProject.executorPMInfo = await this.getUserInfo(project.executorPM);
      transformedProject.contentMediaInfo = await Promise.all(
        project.contentMediaIds.map(id => this.getUserInfo(id))
      );
    } catch (error) {
      console.warn(`获取项目 ${project.id} 的用户信息失败:`, error);
      // 设置默认值以避免字段缺失
      transformedProject.executorPMInfo = {
        userid: project.executorPM,
        name: `用户${project.executorPM}`,
        department: '未知部门'
      };
      transformedProject.contentMediaInfo = project.contentMediaIds.map(id => ({
        userid: id,
        name: `用户${id}`,
        department: '未知部门'
      }));
    }

    return transformedProject;
  }

  async getProject(id: string) {
    const project = await this.prisma.project.findUnique({
      where: { id },
      include: {
        brand: true,
        attachments: true,
      },
    });

    if (!project) {
      return null;
    }

    const transformedProject = this.transformProject(project);

    // 填充用户信息
    try {
      transformedProject.executorPMInfo = await this.getUserInfo(project.executorPM);
      transformedProject.contentMediaInfo = await Promise.all(
        project.contentMediaIds.map(id => this.getUserInfo(id))
      );
    } catch (error) {
      console.warn(`获取项目 ${project.id} 的用户信息失败:`, error);
      // 设置默认值以避免字段缺失
      transformedProject.executorPMInfo = {
        userid: project.executorPM,
        name: `用户${project.executorPM}`,
        department: '未知部门'
      };
      transformedProject.contentMediaInfo = project.contentMediaIds.map(id => ({
        userid: id,
        name: `用户${id}`,
        department: '未知部门'
      }));
    }

    return transformedProject;
  }

  async updateProject(data: UpdateProjectRequest, updatedBy: string) {
    const updateData: Prisma.ProjectUpdateInput = {
      updatedBy,
    };

    if (data.documentType) updateData.documentType = data.documentType.toUpperCase() as any;
    if (data.projectName) updateData.projectName = data.projectName;
    if (data.period) {
      updateData.startDate = new Date(data.period.startDate);
      updateData.endDate = new Date(data.period.endDate);
    }
    if (data.budget) {
      if (data.budget.planningBudget !== undefined) updateData.planningBudget = new Decimal(data.budget.planningBudget);
      if (data.budget.influencerBudget !== undefined) updateData.influencerBudget = new Decimal(data.budget.influencerBudget);
      if (data.budget.adBudget !== undefined) updateData.adBudget = new Decimal(data.budget.adBudget);
      if (data.budget.otherBudget !== undefined) updateData.otherBudget = new Decimal(data.budget.otherBudget);
    }
    if (data.cost) {
      if (data.cost.influencerCost !== undefined) updateData.influencerCost = new Decimal(data.cost.influencerCost);
      if (data.cost.adCost !== undefined) updateData.adCost = new Decimal(data.cost.adCost);
      if (data.cost.otherCost !== undefined) updateData.otherCost = new Decimal(data.cost.otherCost);
      if (data.cost.estimatedInfluencerRebate !== undefined) updateData.estimatedInfluencerRebate = new Decimal(data.cost.estimatedInfluencerRebate);
    }
    if (data.executorPM) updateData.executorPM = data.executorPM;
    if (data.contentMediaIds) updateData.contentMediaIds = data.contentMediaIds;
    if (data.contractType) updateData.contractType = data.contractType.toUpperCase() as any;
    if (data.contractSigningStatus) updateData.contractSigningStatus = data.contractSigningStatus.toUpperCase() as any;
    if (data.settlementRules) updateData.settlementRules = data.settlementRules;
    if (data.kpi) updateData.kpi = data.kpi;
    if (data.expectedPaymentMonth !== undefined) updateData.expectedPaymentMonth = data.expectedPaymentMonth;
    if (data.paymentTermDays !== undefined) updateData.paymentTermDays = data.paymentTermDays;
    if (data.brandId) {
      updateData.brand = {
        connect: { id: data.brandId }
      };
    }

    const project = await this.prisma.project.update({
      where: { id: data.id },
      data: updateData,
      include: {
        brand: true,
        attachments: true,
      },
    });

    // 如果提供了附件ID，更新附件关联
    if (data.attachmentIds !== undefined) {
      // 先清除现有的附件关联（将项目ID设为null）
      await this.prisma.attachment.updateMany({
        where: {
          projectId: data.id
        },
        data: {
          projectId: null
        }
      });

      // 如果有新的附件ID，建立新的关联
      if (data.attachmentIds.length > 0) {
        await this.prisma.attachment.updateMany({
          where: {
            id: {
              in: data.attachmentIds
            }
          },
          data: {
            projectId: data.id
          }
        });
      }
    }

    // 重新获取项目数据（包含更新后的附件）
    const projectWithAttachments = await this.prisma.project.findUnique({
      where: { id: data.id },
      include: {
        brand: true,
        attachments: true,
      },
    });

    return this.transformProject(projectWithAttachments!);
  }

  async deleteProject(id: string) {
    await this.prisma.project.delete({
      where: { id },
    });
    return true;
  }

  // 品牌管理
  async createBrand(data: CreateBrandRequest, createdBy: string) {
    const brandData: Prisma.BrandCreateInput = {
      name: data.name,
      createdBy,
    };

    if (data.description !== undefined) {
      brandData.description = data.description;
    }
    if (data.logo !== undefined) {
      brandData.logo = data.logo;
    }

    if (data.status) {
      brandData.status = data.status.toUpperCase() as any;
    }

    return this.prisma.brand.create({
      data: brandData,
    });
  }

  async getBrands(params: BrandQueryParams = {}) {
    const {
      page = 1,
      pageSize = 50,
      status,
      keyword,
      sortBy = 'name',
      sortOrder = 'asc'
    } = params;

    const skip = (page - 1) * pageSize;
    const take = pageSize;

    const where: Prisma.BrandWhereInput = {};
    if (status) where.status = status.toUpperCase() as any;
    if (keyword) {
      where.name = {
        contains: keyword,
        mode: 'insensitive'
      };
    }

    const orderBy: Prisma.BrandOrderByWithRelationInput = {};
    orderBy[sortBy as keyof Prisma.BrandOrderByWithRelationInput] = sortOrder;

    const [brands, total] = await Promise.all([
      this.prisma.brand.findMany({
        skip,
        take,
        where,
        orderBy,
      }),
      this.prisma.brand.count({ where }),
    ]);

    return {
      brands,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    };
  }

  async getBrand(id: string) {
    return this.prisma.brand.findUnique({
      where: { id },
    });
  }

  async updateBrand(data: UpdateBrandRequest) {
    const updateData: Prisma.BrandUpdateInput = {};

    if (data.name) updateData.name = data.name;
    if (data.description !== undefined) updateData.description = data.description;
    if (data.logo !== undefined) updateData.logo = data.logo;
    if (data.status) updateData.status = data.status.toUpperCase() as any;

    return this.prisma.brand.update({
      where: { id: data.id },
      data: updateData,
    });
  }

  async deleteBrand(id: string) {
    // 检查是否有关联项目
    const projectCount = await this.prisma.project.count({
      where: { brandId: id }
    });

    if (projectCount > 0) {
      throw new Error('无法删除品牌，存在关联的项目');
    }

    await this.prisma.brand.delete({
      where: { id },
    });
    return true;
  }

  async createBrandsBatch(brands: Array<{ name: string; description?: string; logo?: string; status?: 'active' | 'inactive' }>, createdBy: string) {
    const createData = brands.map(brand => ({
      name: brand.name,
      description: brand.description,
      logo: brand.logo,
      status: brand.status ? brand.status.toUpperCase() as any : 'ACTIVE',
      createdBy,
    }));

    const results = await this.prisma.brand.createMany({
      data: createData,
      skipDuplicates: true,
    });

    return results;
  }

  // 统计查询
  async getProjectStats(): Promise<ProjectStats> {
    const [
      totalProjects,
      activeProjects,
      completedProjects,
      budgetSum,
      profitSum,
      projectsByBrand,
      projectsByContract,
    ] = await Promise.all([
      this.prisma.project.count(),
      this.prisma.project.count({ where: { status: 'ACTIVE' } }),
      this.prisma.project.count({ where: { status: 'COMPLETED' } }),
      this.prisma.project.aggregate({
        _sum: {
          planningBudget: true,
        },
      }),
      this.prisma.project.findMany({
        select: {
          planningBudget: true,
          influencerCost: true,
          adCost: true,
          otherCost: true,
          estimatedInfluencerRebate: true,
        },
      }),
      this.prisma.project.groupBy({
        by: ['brandId'],
        _count: true,
        _sum: {
          planningBudget: true,
        },
      }),
      this.prisma.project.groupBy({
        by: ['contractType'],
        _count: true,
        _sum: {
          planningBudget: true,
        },
      }),
    ]);

    // 计算总利润和平均毛利率
    let totalProfit = 0;
    let totalGrossMargin = 0;

    for (const project of profitSum) {
      const totalCost = Number(project.influencerCost) + Number(project.adCost) + Number(project.otherCost);
      const profit = Number(project.planningBudget) - totalCost + Number(project.estimatedInfluencerRebate);
      const grossMargin = Number(project.planningBudget) > 0 ? (profit / Number(project.planningBudget)) * 100 : 0;

      totalProfit += profit;
      totalGrossMargin += grossMargin;
    }

    const averageGrossMargin = profitSum.length > 0 ? totalGrossMargin / profitSum.length : 0;

    // 获取品牌名称
    const brandIds = projectsByBrand.map(item => item.brandId);
    const brands = await this.prisma.brand.findMany({
      where: { id: { in: brandIds } },
      select: { id: true, name: true },
    });

    const brandMap = new Map(brands.map(brand => [brand.id, brand.name]));

    // 获取收入统计
    const revenueStats = await this.getRevenueStats();

    // 获取周预算统计
    const weeklyBudgetStats = await this.getWeeklyBudgetStats();

    // 获取供应商统计
    const supplierStats = await this.getSupplierStats();

    return {
      totalProjects,
      activeProjects,
      completedProjects,
      totalBudget: Number(budgetSum._sum.planningBudget || 0),
      totalProfit: Math.round(totalProfit * 100) / 100,
      averageGrossMargin: Math.round(averageGrossMargin * 100) / 100,
      revenueStats,
      weeklyBudgetStats,
      supplierStats,
      projectsByBrand: projectsByBrand.map(stat => ({
        brandId: stat.brandId,
        brandName: brandMap.get(stat.brandId) || '未知品牌',
        count: stat._count,
        totalBudget: Number(stat._sum.planningBudget || 0),
      })),
      projectsByContractType: projectsByContract.map(stat => ({
        contractType: stat.contractType as any,
        count: stat._count,
        totalBudget: Number(stat._sum.planningBudget || 0),
      })),
    };
  }

  // 获取用户信息（优先从本地数据库，必要时从钉钉API同步）
  private async getUserInfo(userid: string): Promise<User> {
    try {
      // 首先尝试从本地数据库获取用户信息
      const localUser = await this.getUser(userid);

      if (localUser && localUser.isActive) {
        // 检查是否需要同步（超过24小时）
        const syncIntervalHours = 24;
        const cutoffTime = new Date(Date.now() - syncIntervalHours * 60 * 60 * 1000);

        if (localUser.lastSyncAt > cutoffTime) {
          // 本地数据是最新的，直接返回
          return {
            userid: localUser.userid,
            name: localUser.name,
            avatar: localUser.avatar || '',
            department: localUser.deptIdList?.map(String).join(', ') || '未知部门'
          };
        }
      }

      // 本地没有数据或数据过期，返回默认值
      // 注意：实际的用户同步应该通过 UserSyncService 在后台进行
      return {
        userid,
        name: `用户${userid}`,
        avatar: '',
        department: '未知部门'
      };
    } catch (error) {
      console.warn(`获取用户信息失败: ${userid}`, error);
      return {
        userid,
        name: `用户${userid}`,
        department: '未知部门'
      };
    }
  }

  // 部门相关方法

  /**
   * 插入或更新部门信息
   */
  async upsertDepartment(departmentData: {
    deptId: number;
    name: string;
    parentId: number;
    createDeptGroup?: boolean;
    autoAddUser?: boolean;
    fromUnionOrg?: boolean;
    tags?: string;
    order?: number;
    deptManagerUseridList?: string[];
    outerDept?: boolean;
    outerPermitDepts?: number[];
    outerPermitUsers?: string[];
    orgDeptOwner?: string;
    deptPerimits?: number;
    userPerimits?: number;
    outerDeptOnlySelf?: boolean;
    sourceIdentifier?: string;
    ext?: string;
    hideSceneConfig?: any;
  }): Promise<void> {
    const data = {
      deptId: departmentData.deptId,
      name: departmentData.name,
      parentId: departmentData.parentId,
      createDeptGroup: departmentData.createDeptGroup || false,
      autoAddUser: departmentData.autoAddUser || false,
      fromUnionOrg: departmentData.fromUnionOrg || false,
      tags: departmentData.tags || null,
      order: departmentData.order || 0,
      deptManagerUseridList: departmentData.deptManagerUseridList || [],
      outerDept: departmentData.outerDept || false,
      outerPermitDepts: departmentData.outerPermitDepts || [],
      outerPermitUsers: departmentData.outerPermitUsers || [],
      orgDeptOwner: departmentData.orgDeptOwner || null,
      deptPerimits: departmentData.deptPerimits || 0,
      userPerimits: departmentData.userPerimits || 0,
      outerDeptOnlySelf: departmentData.outerDeptOnlySelf || false,
      sourceIdentifier: departmentData.sourceIdentifier || null,
      ext: departmentData.ext || null,
      hideSceneConfig: departmentData.hideSceneConfig || null,
      lastSyncAt: new Date()
    };

    await this.prisma.department.upsert({
      where: { deptId: departmentData.deptId },
      update: data,
      create: data
    });
  }

  /**
   * 获取部门信息
   */
  async getDepartment(deptId: number): Promise<{
    deptId: number;
    name: string;
    parentId: number;
    createDeptGroup: boolean;
    autoAddUser: boolean;
    fromUnionOrg: boolean;
    tags?: string;
    order: number;
    deptManagerUseridList: string[];
    outerDept: boolean;
    outerPermitDepts: number[];
    outerPermitUsers: string[];
    orgDeptOwner?: string;
    deptPerimits: number;
    userPerimits: number;
    outerDeptOnlySelf: boolean;
    sourceIdentifier?: string;
    ext?: string;
    hideSceneConfig?: any;
    lastSyncAt?: Date;
  } | null> {
    const dept = await this.prisma.department.findUnique({
      where: { deptId }
    });

    if (!dept) {
      return null;
    }

    return {
      deptId: dept.deptId,
      name: dept.name,
      parentId: dept.parentId,
      createDeptGroup: dept.createDeptGroup,
      autoAddUser: dept.autoAddUser,
      fromUnionOrg: dept.fromUnionOrg,
      tags: dept.tags || undefined,
      order: dept.order,
      deptManagerUseridList: dept.deptManagerUseridList,
      outerDept: dept.outerDept,
      outerPermitDepts: dept.outerPermitDepts,
      outerPermitUsers: dept.outerPermitUsers,
      orgDeptOwner: dept.orgDeptOwner || undefined,
      deptPerimits: dept.deptPerimits,
      userPerimits: dept.userPerimits,
      outerDeptOnlySelf: dept.outerDeptOnlySelf,
      sourceIdentifier: dept.sourceIdentifier || undefined,
      ext: dept.ext || undefined,
      hideSceneConfig: dept.hideSceneConfig || undefined,
      lastSyncAt: dept.lastSyncAt
    };
  }

  /**
   * 获取所有部门
   */
  async getAllDepartments(): Promise<Array<{
    deptId: number;
    name: string;
    parentId: number;
    order: number;
  }>> {
    const departments = await this.prisma.department.findMany({
      select: {
        deptId: true,
        name: true,
        parentId: true,
        order: true
      },
      orderBy: [
        { order: 'asc' },
        { name: 'asc' }
      ]
    });

    return departments;
  }

  /**
   * 搜索部门
   */
  async searchDepartments(keyword: string): Promise<Array<{
    deptId: number;
    name: string;
    parentId: number;
    order: number;
  }>> {
    const departments = await this.prisma.department.findMany({
      where: {
        name: {
          contains: keyword,
          mode: 'insensitive'
        }
      },
      select: {
        deptId: true,
        name: true,
        parentId: true,
        order: true
      },
      orderBy: [
        { order: 'asc' },
        { name: 'asc' }
      ]
    });

    return departments;
  }

  /**
   * 获取子部门
   */
  async getSubDepartments(parentId: number): Promise<Array<{
    deptId: number;
    name: string;
    parentId: number;
  }>> {
    const departments = await this.prisma.department.findMany({
      where: { parentId },
      select: {
        deptId: true,
        name: true,
        parentId: true
      },
      orderBy: [
        { order: 'asc' },
        { name: 'asc' }
      ]
    });

    return departments;
  }

  // 数据转换方法
  private transformProject(project: any): Project {
    // 计算利润
    const totalCost = Number(project.influencerCost) + Number(project.adCost) + Number(project.otherCost);
    const profit = Number(project.planningBudget) - totalCost + Number(project.estimatedInfluencerRebate);
    const grossMargin = Number(project.planningBudget) > 0 ? (profit / Number(project.planningBudget)) * 100 : 0;

    return {
      id: project.id,
      documentType: project.documentType,
      brandId: project.brandId,
      brand: project.brand,
      projectName: project.projectName,
      period: {
        startDate: project.startDate,
        endDate: project.endDate,
      },
      budget: {
        planningBudget: Number(project.planningBudget),
        influencerBudget: Number(project.influencerBudget),
        adBudget: Number(project.adBudget),
        otherBudget: Number(project.otherBudget),
      },
      cost: {
        influencerCost: Number(project.influencerCost),
        adCost: Number(project.adCost),
        otherCost: Number(project.otherCost),
        estimatedInfluencerRebate: Number(project.estimatedInfluencerRebate),
      },
      profit: {
        profit: Math.round(profit * 100) / 100,
        grossMargin: Math.round(grossMargin * 100) / 100,
      },
      executorPM: project.executorPM,
      contentMediaIds: project.contentMediaIds,
      contractType: project.contractType,
      contractSigningStatus: project.contractSigningStatus,
      settlementRules: project.settlementRules,
      kpi: project.kpi,
      expectedPaymentMonth: project.expectedPaymentMonth,
      paymentTermDays: project.paymentTermDays,
      attachments: project.attachments || [],
      status: project.status,
      createdAt: project.createdAt,
      updatedAt: project.updatedAt,
      createdBy: project.createdBy,
      updatedBy: project.updatedBy,
    };
  }

  // 项目收入管理
  async createProjectRevenue(projectId: string, data: CreateProjectRevenueRequest, createdBy: string) {
    const revenueData: any = {
      title: data.title,
      revenueType: data.revenueType.toUpperCase() as any,
      plannedAmount: new Decimal(data.plannedAmount),
      milestone: data.milestone,
      paymentTerms: data.paymentTerms,
      notes: data.notes,
      createdBy,
      updatedBy: createdBy,
      projectId: projectId
    };

    // 只有当 plannedDate 存在时才添加到数据中
    if (data.plannedDate) {
      revenueData.plannedDate = new Date(data.plannedDate);
    }

    const revenue = await this.prisma.projectRevenue.create({
      data: revenueData,
      include: {
        project: {
          include: {
            brand: true
          }
        }
      }
    });

    return this.transformProjectRevenue(revenue);
  }

  async getProjectRevenues(params: ProjectRevenueQueryParams = {}) {
    const {
      page = 1,
      pageSize = 20,
      projectId,
      status,
      revenueType,
      startDate,
      endDate,
      sortBy = 'plannedDate',
      sortOrder = 'asc'
    } = params;

    const skip = (page - 1) * pageSize;
    const take = pageSize;

    // 构建查询条件
    const where: any = {};

    if (projectId) where.projectId = projectId;
    if (status) where.status = status.toUpperCase() as any;
    if (revenueType) where.revenueType = revenueType.toUpperCase() as any;
    if (startDate) where.plannedDate = { gte: new Date(startDate) };
    if (endDate) where.plannedDate = { ...where.plannedDate, lte: new Date(endDate) };

    // 构建排序条件
    const orderBy: any = {};
    orderBy[sortBy] = sortOrder;

    const [revenues, total] = await Promise.all([
      this.prisma.projectRevenue.findMany({
        skip,
        take,
        where,
        orderBy,
        include: {
          project: {
            include: {
              brand: true
            }
          }
        }
      }),
      this.prisma.projectRevenue.count({ where })
    ]);

    return {
      revenues: revenues.map(this.transformProjectRevenue.bind(this)),
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    };
  }

  async getProjectRevenue(id: string) {
    const revenue = await this.prisma.projectRevenue.findUnique({
      where: { id },
      include: {
        project: {
          include: {
            brand: true
          }
        }
      }
    });

    return revenue ? this.transformProjectRevenue(revenue) : null;
  }

  async updateProjectRevenue(data: UpdateProjectRevenueRequest, updatedBy: string) {
    const updateData: any = {
      updatedBy,
    };

    if (data.title) updateData.title = data.title;
    if (data.revenueType) updateData.revenueType = data.revenueType.toUpperCase() as any;
    if (data.status) updateData.status = data.status.toUpperCase() as any;
    if (data.plannedAmount !== undefined) updateData.plannedAmount = new Decimal(data.plannedAmount);
    if (data.actualAmount !== undefined) updateData.actualAmount = new Decimal(data.actualAmount);
    if (data.invoiceAmount !== undefined) updateData.invoiceAmount = new Decimal(data.invoiceAmount);
    if (data.plannedDate) updateData.plannedDate = new Date(data.plannedDate);
    if (data.confirmedDate) updateData.confirmedDate = new Date(data.confirmedDate);
    if (data.invoiceDate) updateData.invoiceDate = new Date(data.invoiceDate);
    if (data.receivedDate) updateData.receivedDate = new Date(data.receivedDate);
    if (data.milestone !== undefined) updateData.milestone = data.milestone;
    if (data.invoiceNumber !== undefined) updateData.invoiceNumber = data.invoiceNumber;
    if (data.paymentTerms !== undefined) updateData.paymentTerms = data.paymentTerms;
    if (data.notes !== undefined) updateData.notes = data.notes;

    const revenue = await this.prisma.projectRevenue.update({
      where: { id: data.id },
      data: updateData,
      include: {
        project: {
          include: {
            brand: true
          }
        }
      }
    });

    return this.transformProjectRevenue(revenue);
  }

  async deleteProjectRevenue(id: string) {
    await this.prisma.projectRevenue.delete({
      where: { id }
    });
    return true;
  }

  // 获取收入统计
  async getRevenueStats(): Promise<RevenueStats> {
    // 暂时返回空统计，等Prisma客户端生成后实现
    return {
      totalPlannedRevenue: 0,
      totalActualRevenue: 0,
      totalInvoicedRevenue: 0,
      totalReceivedRevenue: 0,
      revenueByStatus: [],
      revenueByType: [],
      monthlyRevenueTrend: []
    };
  }

  // 确认项目收入
  async confirmProjectRevenue(id: string, data: any, updatedBy: string) {
    const updateData: any = {
      status: 'RECEIVED', // 确认收入状态为已收款
      actualAmount: new Decimal(data.actualAmount),
      confirmedDate: data.confirmedDate ? new Date(data.confirmedDate) : new Date(),
      updatedBy,
    };

    // 如果提供了备注，更新备注
    if (data.notes !== undefined) {
      updateData.notes = data.notes;
    }

    const revenue = await this.prisma.projectRevenue.update({
      where: { id },
      data: updateData,
      include: {
        project: {
          include: {
            brand: true
          }
        }
      }
    });

    return this.transformProjectRevenue(revenue);
  }

  // 批量确认项目收入
  async batchConfirmProjectRevenues(revenues: Array<{
    id: string;
    actualAmount: number;
    confirmedDate?: string;
    notes?: string;
  }>, updatedBy: string) {
    const results = [];
    let successCount = 0;
    let failureCount = 0;

    for (const revenueData of revenues) {
      try {
        const revenue = await this.confirmProjectRevenue(revenueData.id, revenueData, updatedBy);
        results.push({
          id: revenueData.id,
          success: true,
          data: revenue
        });
        successCount++;
      } catch (error) {
        results.push({
          id: revenueData.id,
          success: false,
          error: error instanceof Error ? error.message : '确认收入失败'
        });
        failureCount++;
      }
    }

    return {
      successCount,
      failureCount,
      results
    };
  }

  // 数据转换方法
  private transformProjectRevenue(revenue: any): ProjectRevenue {
    return {
      id: revenue.id,
      title: revenue.title,
      revenueType: revenue.revenueType.toLowerCase(),
      status: revenue.status.toLowerCase(),
      plannedAmount: Number(revenue.plannedAmount),
      actualAmount: revenue.actualAmount ? Number(revenue.actualAmount) : undefined,
      invoiceAmount: revenue.invoiceAmount ? Number(revenue.invoiceAmount) : undefined,
      plannedDate: revenue.plannedDate,
      confirmedDate: revenue.confirmedDate || undefined,
      invoiceDate: revenue.invoiceDate || undefined,
      receivedDate: revenue.receivedDate || undefined,
      milestone: revenue.milestone || undefined,
      invoiceNumber: revenue.invoiceNumber || undefined,
      paymentTerms: revenue.paymentTerms || undefined,
      notes: revenue.notes || undefined,
      projectId: revenue.projectId,
      createdAt: revenue.createdAt,
      updatedAt: revenue.updatedAt,
      createdBy: revenue.createdBy,
      updatedBy: revenue.updatedBy,
    };
  }

  // 供应商管理
  async createSupplier(data: CreateSupplierRequest, createdBy: string) {
    const supplierData = {
      name: data.name,
      shortName: data.shortName,
      code: data.code,
      contactPerson: data.contactPerson,
      contactPhone: data.contactPhone,
      contactEmail: data.contactEmail,
      address: data.address,
      taxNumber: data.taxNumber,
      bankAccount: data.bankAccount,
      bankName: data.bankName,
      legalPerson: data.legalPerson,
      serviceTypes: data.serviceTypes.map(type => type.toUpperCase()) as any,
      preferredTaxRate: data.preferredTaxRate?.toUpperCase() as any,
      creditLimit: data.creditLimit ? new Decimal(data.creditLimit) : undefined,
      paymentTerms: data.paymentTerms,
      rating: data.rating,
      notes: data.notes,
      createdBy,
      updatedBy: createdBy
    };

    const supplier = await this.prisma.supplier.create({
      data: supplierData
    });

    return this.transformSupplier(supplier);
  }

  async createSuppliersBatch(suppliers: Array<z.infer<typeof createSupplierSchema>>, createdBy: string) {
    const createData = suppliers.map(supplier => ({
      name: supplier.name,
      shortName: supplier.shortName,
      code: supplier.code,
      contactPerson: supplier.contactPerson,
      contactPhone: supplier.contactPhone,
      contactEmail: supplier.contactEmail,
      address: supplier.address,
      taxNumber: supplier.taxNumber,
      bankAccount: supplier.bankAccount,
      bankName: supplier.bankName,
      legalPerson: supplier.legalPerson,
      serviceTypes: supplier.serviceTypes.map(type => type.toUpperCase()) as any,
      preferredTaxRate: supplier.preferredTaxRate?.toUpperCase() as any,
      creditLimit: supplier.creditLimit ? new Decimal(supplier.creditLimit) : undefined,
      paymentTerms: supplier.paymentTerms,
      rating: supplier.rating,
      notes: supplier.notes,
      createdBy,
      updatedBy: createdBy
    }));

    const results = await this.prisma.supplier.createMany({
      data: createData,
      skipDuplicates: true,
    });

    return results;
  }

  async getSuppliers(params: SupplierQueryParams = {}) {
    const {
      page = 1,
      pageSize = 20,
      status,
      serviceType,
      keyword,
      sortBy = 'name',
      sortOrder = 'asc'
    } = params;

    const skip = (page - 1) * pageSize;
    const take = pageSize;

    const where: any = {};

    if (status) where.status = status.toUpperCase() as any;
    if (serviceType) where.serviceTypes = { has: serviceType.toUpperCase() as any };
    if (keyword) {
      where.OR = [
        { name: { contains: keyword, mode: 'insensitive' } },
        { shortName: { contains: keyword, mode: 'insensitive' } },
        { contactPerson: { contains: keyword, mode: 'insensitive' } }
      ];
    }

    const orderBy: any = {};
    orderBy[sortBy] = sortOrder;

    const [suppliers, total] = await Promise.all([
      this.prisma.supplier.findMany({
        skip,
        take,
        where,
        orderBy
      }),
      this.prisma.supplier.count({ where })
    ]);

    return {
      suppliers: suppliers.map(this.transformSupplier.bind(this)),
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    };
  }

  async getSupplier(id: string) {
    const supplier = await this.prisma.supplier.findUnique({
      where: { id }
    });

    return supplier ? this.transformSupplier(supplier) : null;
  }

  async updateSupplier(data: UpdateSupplierRequest, updatedBy: string) {
    const updateData: any = {
      updatedBy,
    };

    if (data.name) updateData.name = data.name;
    if (data.shortName !== undefined) updateData.shortName = data.shortName;
    if (data.code !== undefined) updateData.code = data.code;
    if (data.contactPerson !== undefined) updateData.contactPerson = data.contactPerson;
    if (data.contactPhone !== undefined) updateData.contactPhone = data.contactPhone;
    if (data.contactEmail !== undefined) updateData.contactEmail = data.contactEmail;
    if (data.address !== undefined) updateData.address = data.address;
    if (data.taxNumber !== undefined) updateData.taxNumber = data.taxNumber;
    if (data.bankAccount !== undefined) updateData.bankAccount = data.bankAccount;
    if (data.bankName !== undefined) updateData.bankName = data.bankName;
    if (data.legalPerson !== undefined) updateData.legalPerson = data.legalPerson;
    if (data.serviceTypes) updateData.serviceTypes = data.serviceTypes.map(type => type.toUpperCase()) as any;
    if (data.preferredTaxRate !== undefined) updateData.preferredTaxRate = data.preferredTaxRate?.toUpperCase() as any;
    if (data.creditLimit !== undefined) updateData.creditLimit = data.creditLimit ? new Decimal(data.creditLimit) : null;
    if (data.paymentTerms !== undefined) updateData.paymentTerms = data.paymentTerms;
    if (data.status) updateData.status = data.status.toUpperCase() as any;
    if (data.rating !== undefined) updateData.rating = data.rating;
    if (data.notes !== undefined) updateData.notes = data.notes;

    const supplier = await this.prisma.supplier.update({
      where: { id: data.id },
      data: updateData
    });

    return this.transformSupplier(supplier);
  }

  async deleteSupplier(id: string) {
    await this.prisma.supplier.delete({
      where: { id }
    });
    return true;
  }

  // 获取供应商统计
  async getSupplierStats(): Promise<SupplierStats> {
    const totalSuppliers = await this.prisma.supplier.count();
    const activeSuppliers = await this.prisma.supplier.count({ where: { status: 'ACTIVE' } });
    const suppliersByServiceType = await this.prisma.supplier.groupBy({
      by: ['serviceTypes'],
      _count: true
    });
    const suppliersByRating = await this.prisma.supplier.groupBy({
      by: ['rating'],
      _count: true
    });
    console.log('suppliersByRating', suppliersByRating);
    return {
      totalSuppliers: totalSuppliers,
      activeSuppliers: activeSuppliers,
      suppliersByServiceType: suppliersByServiceType.map(item => ({
        serviceType: item.serviceTypes[0] as any,
        count: item._count
      })),
      suppliersByRating: suppliersByRating.map(item => ({
        rating: item.rating || 0,
        count: item._count
      }))
    };
  }

  // 周预算管理
  async createWeeklyBudget(projectId: string, data: CreateWeeklyBudgetRequest, createdBy: string) {
    const startDate = new Date(data.weekStartDate);
    const endDate = new Date(data.weekEndDate);
    const year = startDate.getFullYear();
    const weekNumber = this.getWeekNumber(startDate);

    const budgetData = {
      title: data.title,
      weekStartDate: startDate,
      weekEndDate: endDate,
      weekNumber,
      year,
      serviceType: data.serviceType.toUpperCase() as any,
      serviceContent: data.serviceContent,
      remarks: data.remarks,
      contractAmount: new Decimal(data.contractAmount),
      taxRate: data.taxRate.toUpperCase() as any,
      remainingAmount: new Decimal(data.contractAmount),
      projectId,
      status: data.status?.toUpperCase() as any || WeeklyBudgetStatus.DRAFT.toUpperCase() as any,
      supplierId: data.supplierId,
      createdBy,
      updatedBy: createdBy
    };

    const budget = await this.prisma.weeklyBudget.create({
      data: budgetData,
      include: {
        project: {
          include: {
            brand: true
          }
        },
        supplier: true
      }
    });

    return this.transformWeeklyBudget(budget);
  }

  async getWeeklyBudgets(params: WeeklyBudgetQueryParams = {}) {
    const {
      page = 1,
      pageSize = 20,
      projectId,
      supplierId,
      serviceType,
      status,
      year,
      weekNumber,
      startDate,
      endDate,
      sortBy = 'weekStartDate',
      sortOrder = 'asc'
    } = params;

    const skip = (page - 1) * pageSize;
    const take = pageSize;

    const where: any = {};

    if (projectId) where.projectId = projectId;
    if (supplierId) where.supplierId = supplierId;
    if (serviceType) where.serviceType = serviceType.toUpperCase() as any;
    if (status) where.status = status.toUpperCase() as any;
    if (year) where.year = year;
    if (weekNumber) where.weekNumber = weekNumber;
    if (startDate) where.weekStartDate = { gte: new Date(startDate) };
    if (endDate) where.weekEndDate = { lte: new Date(endDate) };

    const orderBy: any = {};
    orderBy[sortBy] = sortOrder;

    const [budgets, total] = await Promise.all([
      this.prisma.weeklyBudget.findMany({
        skip,
        take,
        where,
        orderBy,
        include: {
          project: {
            include: {
              brand: true
            }
          },
          supplier: true
        }
      }),
      this.prisma.weeklyBudget.count({ where })
    ]);

    return {
      weeklyBudgets: budgets.map(this.transformWeeklyBudget.bind(this)),
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    };
  }

  async getWeeklyBudget(id: string) {
    const budget = await this.prisma.weeklyBudget.findUnique({
      where: { id },
      include: {
        project: {
          include: {
            brand: true
          }
        },
        supplier: true
      }
    });

    return budget ? this.transformWeeklyBudget(budget) : null;
  }

  async updateWeeklyBudget(data: UpdateWeeklyBudgetRequest, updatedBy: string) {
    const updateData: any = {
      updatedBy,
    };

    if (data.title) updateData.title = data.title;
    if (data.weekStartDate) {
      const startDate = new Date(data.weekStartDate);
      updateData.weekStartDate = startDate;
      updateData.year = startDate.getFullYear();
      updateData.weekNumber = this.getWeekNumber(startDate);
    }
    if (data.weekEndDate) updateData.weekEndDate = new Date(data.weekEndDate);
    if (data.serviceType) updateData.serviceType = data.serviceType.toUpperCase() as any;
    if (data.serviceContent) updateData.serviceContent = data.serviceContent;
    if (data.remarks !== undefined) updateData.remarks = data.remarks;
    if (data.contractAmount !== undefined) {
      updateData.contractAmount = new Decimal(data.contractAmount);
      // 重新计算剩余金额
      const currentBudget = await this.prisma.weeklyBudget.findUnique({ where: { id: data.id } });
      if (currentBudget) {
        updateData.remainingAmount = new Decimal(data.contractAmount).sub(currentBudget.paidAmount);
      }
    }
    if (data.taxRate) updateData.taxRate = data.taxRate.toUpperCase() as any;
    if (data.status) updateData.status = data.status.toUpperCase() as any;
    if (data.paidAmount !== undefined) {
      updateData.paidAmount = new Decimal(data.paidAmount);
      // 重新计算剩余金额
      const currentBudget = await this.prisma.weeklyBudget.findUnique({ where: { id: data.id } });
      if (currentBudget) {
        updateData.remainingAmount = currentBudget.contractAmount.sub(new Decimal(data.paidAmount));
      }
    }
    if (data.supplierId !== undefined) updateData.supplierId = data.supplierId;

    const budget = await this.prisma.weeklyBudget.update({
      where: { id: data.id },
      data: updateData,
      include: {
        project: {
          include: {
            brand: true
          }
        },
        supplier: true
      }
    });

    return this.transformWeeklyBudget(budget);
  }

  async deleteWeeklyBudget(id: string) {
    await this.prisma.weeklyBudget.delete({
      where: { id }
    });
    return true;
  }

  async getWeeklyBudgetStats(): Promise<WeeklyBudgetStats> {
    // 暂时返回空统计，等Prisma客户端生成后实现
    return {
      totalBudgets: 0,
      totalContractAmount: 0,
      totalPaidAmount: 0,
      totalRemainingAmount: 0,
      budgetsByServiceType: [],
      budgetsByStatus: [],
      budgetsBySupplier: [],
      weeklyTrend: []
    };
  }

  async batchCreateWeeklyBudgets(
    projectId: string,
    startDate: string,
    endDate: string,
    serviceType: ServiceType,
    defaultContractAmount: number,
    defaultTaxRate: TaxRate,
    createdBy: string
  ): Promise<WeeklyBudget[]> {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const budgets: WeeklyBudget[] = [];

    // 按周创建预算
    let currentWeekStart = new Date(start);
    let weekCounter = 1;

    while (currentWeekStart <= end) {
      const weekEnd = new Date(currentWeekStart);
      weekEnd.setDate(weekEnd.getDate() + 6); // 一周7天

      if (weekEnd > end) {
        weekEnd.setTime(end.getTime());
      }

      const weekData: CreateWeeklyBudgetRequest = {
        title: `第${weekCounter}周预算`,
        weekStartDate: currentWeekStart,
        weekEndDate: weekEnd,
        serviceType,
        serviceContent: `${serviceType === 'influencer' ? '达人' : serviceType === 'advertising' ? '投流' : '其他'}服务`,
        contractAmount: defaultContractAmount,
        taxRate: defaultTaxRate
      };

      const budget = await this.createWeeklyBudget(projectId, weekData, createdBy);
      budgets.push(budget);

      // 移动到下一周
      currentWeekStart.setDate(currentWeekStart.getDate() + 7);
      weekCounter++;
    }

    return budgets;
  }

  // 辅助方法：获取周数
  private getWeekNumber(date: Date): number {
    const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
    const pastDaysOfYear = (date.getTime() - firstDayOfYear.getTime()) / 86400000;
    return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
  }

  // 数据转换方法
  private transformSupplier(supplier: any): Supplier {
    return {
      id: supplier.id,
      name: supplier.name,
      shortName: supplier.shortName || undefined,
      code: supplier.code || undefined,
      contactPerson: supplier.contactPerson || undefined,
      contactPhone: supplier.contactPhone || undefined,
      contactEmail: supplier.contactEmail || undefined,
      address: supplier.address || undefined,
      taxNumber: supplier.taxNumber || undefined,
      bankAccount: supplier.bankAccount || undefined,
      bankName: supplier.bankName || undefined,
      legalPerson: supplier.legalPerson || undefined,
      serviceTypes: supplier.serviceTypes.map((type: string) => type.toLowerCase()),
      preferredTaxRate: supplier.preferredTaxRate?.toLowerCase() || undefined,
      creditLimit: supplier.creditLimit ? Number(supplier.creditLimit) : undefined,
      paymentTerms: supplier.paymentTerms || undefined,
      status: supplier.status.toLowerCase(),
      rating: supplier.rating || undefined,
      notes: supplier.notes || undefined,
      createdAt: supplier.createdAt,
      updatedAt: supplier.updatedAt,
      createdBy: supplier.createdBy,
      updatedBy: supplier.updatedBy,
    };
  }

  private transformWeeklyBudget(budget: any): WeeklyBudget {
    return {
      id: budget.id,
      title: budget.title,
      weekStartDate: budget.weekStartDate,
      weekEndDate: budget.weekEndDate,
      weekNumber: budget.weekNumber,
      year: budget.year,
      serviceType: budget.serviceType.toLowerCase(),
      serviceContent: budget.serviceContent,
      remarks: budget.remarks || undefined,
      contractAmount: Number(budget.contractAmount),
      taxRate: budget.taxRate.toLowerCase(),
      paidAmount: Number(budget.paidAmount),
      remainingAmount: Number(budget.remainingAmount),
      status: budget.status.toLowerCase(),
      approvalStatus: budget.approvalStatus ? budget.approvalStatus.toLowerCase() : 'none',
      projectId: budget.projectId,
      supplierId: budget.supplierId || undefined,
      supplier: budget.supplier ? this.transformSupplier(budget.supplier) : undefined,
      createdAt: budget.createdAt,
      updatedAt: budget.updatedAt,
      createdBy: budget.createdBy,
      updatedBy: budget.updatedBy,
    };
  }

  // 审批管理
  async createApprovalInstance(data: {
    processInstanceId: string;
    processCode: string;
    businessId?: string;
    title: string;
    originatorUserId: string;
    status: ApprovalStatus;
    createTime: Date;
    approvalAmount: number;
    reason?: string;
    remark?: string;
    weeklyBudgetId: string;
  }): Promise<ApprovalInstance> {
    const approvalData = {
      processInstanceId: data.processInstanceId,
      processCode: data.processCode,
      businessId: data.businessId,
      title: data.title,
      originatorUserId: data.originatorUserId,
      status: data.status,
      createTime: data.createTime,
      approvalAmount: new Decimal(data.approvalAmount),
      reason: data.reason,
      remark: data.remark,
      weeklyBudgetId: data.weeklyBudgetId
    };

    const approval = await this.prisma.approvalInstance.create({
      data: approvalData,
      include: {
        weeklyBudget: {
          include: {
            project: true,
            supplier: true
          }
        }
      }
    });

    return this.transformApprovalInstance(approval);
  }

  async getApprovalInstances(params: {
    page?: number;
    pageSize?: number;
    weeklyBudgetId?: string;
    status?: ApprovalStatus;
    originatorUserId?: string;
    startDate?: string;
    endDate?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  } = {}) {
    const {
      page = 1,
      pageSize = 20,
      weeklyBudgetId,
      status,
      originatorUserId,
      startDate,
      endDate,
      sortBy = 'createTime',
      sortOrder = 'desc'
    } = params;

    const skip = (page - 1) * pageSize;
    const take = pageSize;

    const where: any = {};
    if (weeklyBudgetId) where.weeklyBudgetId = weeklyBudgetId;
    if (status) where.status = status;
    if (originatorUserId) where.originatorUserId = originatorUserId;
    if (startDate) where.createTime = { gte: new Date(startDate) };
    if (endDate) where.createTime = { ...where.createTime, lte: new Date(endDate) };

    const orderBy: any = {};
    orderBy[sortBy] = sortOrder;

    const [approvals, total] = await Promise.all([
      this.prisma.approvalInstance.findMany({
        skip,
        take,
        where,
        orderBy,
        include: {
          weeklyBudget: {
            include: {
              project: true,
              supplier: true
            }
          }
        }
      }),
      this.prisma.approvalInstance.count({ where })
    ]);

    return {
      approvals: approvals.map(this.transformApprovalInstance.bind(this)),
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    };
  }

  async getApprovalInstance(id: string): Promise<ApprovalInstance | null> {
    const approval = await this.prisma.approvalInstance.findUnique({
      where: { id },
      include: {
        weeklyBudget: {
          include: {
            project: true,
            supplier: true
          }
        }
      }
    });

    return approval ? this.transformApprovalInstance(approval) : null;
  }

  async getApprovalInstanceByProcessId(processInstanceId: string): Promise<ApprovalInstance | null> {
    const approval = await this.prisma.approvalInstance.findUnique({
      where: { processInstanceId },
      include: {
        weeklyBudget: {
          include: {
            project: true,
            supplier: true
          }
        }
      }
    });

    return approval ? this.transformApprovalInstance(approval) : null;
  }

  async updateApprovalInstanceStatus(data: {
    processInstanceId: string;
    status: ApprovalStatus;
    result?: string;
    finishTime?: Date;
    actualAmount?: number;
    remark?: string;
  }): Promise<ApprovalInstance | null> {
    const updateData: any = {
      status: data.status,
      result: data.result,
      finishTime: data.finishTime,
      remark: data.remark
    };

    if (data.actualAmount !== undefined) {
      updateData.actualAmount = new Decimal(data.actualAmount);
    }

    const approval = await this.prisma.approvalInstance.update({
      where: { processInstanceId: data.processInstanceId },
      data: updateData,
      include: {
        weeklyBudget: {
          include: {
            project: true,
            supplier: true
          }
        }
      }
    });

    return this.transformApprovalInstance(approval);
  }

  // 转换审批实例数据
  private transformApprovalInstance(approval: any): ApprovalInstance {
    return {
      id: approval.id,
      processInstanceId: approval.processInstanceId,
      processCode: approval.processCode,
      businessId: approval.businessId,
      title: approval.title,
      originatorUserId: approval.originatorUserId,
      status: approval.status.toLowerCase() as ApprovalStatus,
      result: approval.result,
      createTime: approval.createTime,
      finishTime: approval.finishTime,
      approvalAmount: Number(approval.approvalAmount),
      actualAmount: approval.actualAmount ? Number(approval.actualAmount) : undefined,
      reason: approval.reason,
      remark: approval.remark,
      weeklyBudgetId: approval.weeklyBudgetId,
      createdAt: approval.createdAt,
      updatedAt: approval.updatedAt
    };
  }

  // 财务报表方法
  async getBrandFinancialSummary(params: FinancialReportQueryParams = {}): Promise<FinancialReportResponse> {
    console.log('数据库版本 - 获取品牌财务汇总报表:', params);

    // 构建项目查询条件
    const projectWhere: Prisma.ProjectWhereInput = {};

    if (params.brandId) {
      projectWhere.brandId = params.brandId;
    }

    if (params.startDate) {
      projectWhere.startDate = { gte: new Date(params.startDate) };
    }

    if (params.endDate) {
      projectWhere.endDate = { lte: new Date(params.endDate) };
    }

    if (params.projectStatus && params.projectStatus.length > 0) {
      projectWhere.status = {
        in: params.projectStatus.map(status => status.toUpperCase() as any)
      };
    }

    // 如果不包含已完成或已取消的项目，添加过滤条件
    if (params.includeCompleted === false || params.includeCancelled === false) {
      const excludeStatuses = [];
      if (params.includeCompleted === false) excludeStatuses.push('COMPLETED');
      if (params.includeCancelled === false) excludeStatuses.push('CANCELLED');

      if (excludeStatuses.length > 0) {
        projectWhere.status = {
          notIn: excludeStatuses as any[]
        };
      }
    }

    // 获取所有品牌和相关项目数据
    const brands = await this.prisma.brand.findMany({
      where: params.brandId ? { id: params.brandId } : {},
      include: {
        projects: {
          where: projectWhere,
          include: {
            revenues: true,
            weeklyBudgets: true
          }
        }
      }
    });

    console.log(`找到 ${brands.length} 个品牌`);

    // 计算每个品牌的财务汇总
    const brandSummaries: BrandFinancialSummary[] = [];
    let totalOrderAmount = 0;
    let totalExecutedAmount = 0;
    let totalEstimatedProfit = 0;
    let totalReceivedAmount = 0;
    let totalPaidAmount = 0;

    for (const brand of brands) {
      if (brand.projects.length === 0 && !params.brandId) {
        continue; // 跳过没有项目的品牌（除非特定查询某个品牌）
      }

      const summary = await this.calculateBrandFinancialSummary(brand, brand.projects);
      brandSummaries.push(summary);

      totalOrderAmount += summary.orderAmount;
      totalExecutedAmount += summary.executedAmount;
      totalEstimatedProfit += summary.estimatedProfit;
      totalReceivedAmount += summary.receivedAmount;
      totalPaidAmount += summary.paidProjectAmount;
    }

    const overallProfitMargin = totalOrderAmount > 0 ? (totalEstimatedProfit / totalOrderAmount) * 100 : 0;

    console.log('财务汇总计算完成:', {
      totalBrands: brandSummaries.length,
      totalOrderAmount,
      totalEstimatedProfit,
      overallProfitMargin
    });

    return {
      summary: {
        totalBrands: brandSummaries.length,
        totalOrderAmount: Math.round(totalOrderAmount * 100) / 100,
        totalExecutedAmount: Math.round(totalExecutedAmount * 100) / 100,
        totalEstimatedProfit: Math.round(totalEstimatedProfit * 100) / 100,
        totalReceivedAmount: Math.round(totalReceivedAmount * 100) / 100,
        totalPaidAmount: Math.round(totalPaidAmount * 100) / 100,
        overallProfitMargin: Math.round(overallProfitMargin * 100) / 100
      },
      brands: brandSummaries,
      generatedAt: new Date(),
      reportPeriod: {
        startDate: params.startDate ? new Date(params.startDate) : undefined,
        endDate: params.endDate ? new Date(params.endDate) : undefined
      }
    };
  }

  // 计算品牌财务汇总的辅助方法
  private async calculateBrandFinancialSummary(brand: any, projects: any[]): Promise<BrandFinancialSummary> {
    let orderAmount = 0;
    let executedAmount = 0;
    let executingAmount = 0;
    let estimatedProfit = 0;
    let receivedAmount = 0;
    let unreceivedAmount = 0;
    let paidProjectAmount = 0;
    let unpaidProjectAmount = 0;

    let activeProjectCount = 0;
    let completedProjectCount = 0;

    for (const project of projects) {
      // 品牌下单金额 = 项目规划预算总和
      orderAmount += Number(project.planningBudget);

      // 计算项目利润
      const totalCost = Number(project.influencerCost) + Number(project.adCost) + Number(project.otherCost);
      const projectProfit = Number(project.planningBudget) - totalCost + Number(project.estimatedInfluencerRebate);
      estimatedProfit += projectProfit;

      // 根据项目状态计算已执行和执行中金额
      if (project.status === 'COMPLETED') {
        completedProjectCount++;
        // 已执行金额 = 已完成项目的实际支出（成本）
        executedAmount += totalCost;
      } else if (project.status === 'ACTIVE') {
        activeProjectCount++;
        // 执行中项目金额 = 进行中项目的预算
        executingAmount += Number(project.planningBudget);
      } else if (project.status === 'DRAFT') {
        // 草稿状态的项目不计入执行金额
      } else if (project.status === 'CANCELLED') {
        // 已取消的项目不计入执行金额
      }

      // 计算收入数据
      if (project.revenues && project.revenues.length > 0) {
        for (const revenue of project.revenues) {
          if (revenue.status === 'RECEIVED') {
            receivedAmount += Number(revenue.actualAmount || revenue.plannedAmount);
          } else {
            unreceivedAmount += Number(revenue.plannedAmount);
          }
        }
      }

      // 计算周预算支付数据
      if (project.weeklyBudgets && project.weeklyBudgets.length > 0) {
        for (const budget of project.weeklyBudgets) {
          paidProjectAmount += Number(budget.paidAmount);
          unpaidProjectAmount += Number(budget.contractAmount) - Number(budget.paidAmount);
        }
      }
    }

    // 计算预估毛利率
    const estimatedProfitMargin = orderAmount > 0 ? (estimatedProfit / orderAmount) * 100 : 0;

    return {
      brandId: brand.id,
      brandName: brand.name,
      orderAmount: Math.round(orderAmount * 100) / 100,
      executedAmount: Math.round(executedAmount * 100) / 100,
      executingAmount: Math.round(executingAmount * 100) / 100,
      estimatedProfit: Math.round(estimatedProfit * 100) / 100,
      estimatedProfitMargin: Math.round(estimatedProfitMargin * 100) / 100,
      receivedAmount: Math.round(receivedAmount * 100) / 100,
      unreceivedAmount: Math.round(unreceivedAmount * 100) / 100,
      paidProjectAmount: Math.round(paidProjectAmount * 100) / 100,
      unpaidProjectAmount: Math.round(unpaidProjectAmount * 100) / 100,
      projectCount: projects.length,
      activeProjectCount,
      completedProjectCount
    };
  }

  async getBrandFinancialDetail(brandId: string, params: FinancialReportQueryParams = {}): Promise<BrandFinancialDetail> {
    console.log('数据库版本 - 获取品牌财务详细报表:', brandId, params);

    // 获取品牌信息
    const brand = await this.prisma.brand.findUnique({
      where: { id: brandId }
    });

    if (!brand) {
      throw new Error('品牌不存在');
    }

    // 构建项目查询条件
    const projectWhere: Prisma.ProjectWhereInput = {
      brandId: brandId
    };

    if (params.startDate) {
      projectWhere.startDate = { gte: new Date(params.startDate) };
    }

    if (params.endDate) {
      projectWhere.endDate = { lte: new Date(params.endDate) };
    }

    if (params.projectStatus && params.projectStatus.length > 0) {
      projectWhere.status = {
        in: params.projectStatus.map(status => status.toUpperCase() as any)
      };
    }

    // 获取品牌的项目数据
    const projects = await this.prisma.project.findMany({
      where: projectWhere,
      include: {
        revenues: true,
        weeklyBudgets: true
      }
    });

    // 计算品牌财务汇总
    const summary = await this.calculateBrandFinancialSummary(brand, projects);

    // 构建详细项目信息
    const projectDetails = await Promise.all(projects.map(async (project) => {
      const totalBudget = Number(project.planningBudget) + Number(project.influencerBudget) +
                         Number(project.adBudget) + Number(project.otherBudget);
      const totalCost = Number(project.influencerCost) + Number(project.adCost) + Number(project.otherCost);
      const profit = Number(project.planningBudget) - totalCost + Number(project.estimatedInfluencerRebate);
      const grossMargin = Number(project.planningBudget) > 0 ? (profit / Number(project.planningBudget)) * 100 : 0;

      // 计算收入数据
      let plannedAmount = 0;
      let receivedAmount = 0;
      for (const revenue of project.revenues) {
        if (revenue.status === 'RECEIVED') {
          receivedAmount += Number(revenue.actualAmount || revenue.plannedAmount);
        }
        plannedAmount += Number(revenue.plannedAmount);
      }

      // 计算周预算数据
      let totalContractAmount = 0;
      let paidAmount = 0;
      for (const budget of project.weeklyBudgets) {
        totalContractAmount += Number(budget.contractAmount);
        paidAmount += Number(budget.paidAmount);
      }

      // 获取用户信息
      let executorPMInfo;
      try {
        const userInfo = await this.getUserInfo(project.executorPM);
        if (userInfo) {
          executorPMInfo = {
            userid: userInfo.userid,
            name: userInfo.name,
            department: userInfo.department || ''
          };
        }
      } catch (error) {
        console.warn('获取用户信息失败:', error);
      }

      return {
        id: project.id,
        projectName: project.projectName,
        status: project.status as ProjectStatus,
        documentType: project.documentType as any,
        contractType: project.contractType as any,
        period: {
          startDate: project.startDate || new Date(),
          endDate: project.endDate || new Date()
        },
        budget: {
          planningBudget: Number(project.planningBudget),
          totalBudget
        },
        cost: {
          totalCost,
          estimatedInfluencerRebate: Number(project.estimatedInfluencerRebate)
        },
        profit: {
          profit: Math.round(profit * 100) / 100,
          grossMargin: Math.round(grossMargin * 100) / 100
        },
        revenue: {
          plannedAmount: Math.round(plannedAmount * 100) / 100,
          receivedAmount: Math.round(receivedAmount * 100) / 100,
          unreceivedAmount: Math.round((plannedAmount - receivedAmount) * 100) / 100
        },
        weeklyBudgets: {
          totalContractAmount: Math.round(totalContractAmount * 100) / 100,
          paidAmount: Math.round(paidAmount * 100) / 100,
          unpaidAmount: Math.round((totalContractAmount - paidAmount) * 100) / 100
        },
        executorPM: project.executorPM,
        executorPMInfo
      };
    }));

    return {
      brandInfo: {
        id: brand.id,
        name: brand.name,
        description: brand.description || undefined,
        logo: brand.logo || undefined
      },
      summary,
      projects: projectDetails,
      revenueAnalysis: {
        totalPlannedRevenue: 0, // TODO: 实现收入分析
        totalReceivedRevenue: 0,
        revenueByStatus: [],
        monthlyTrend: []
      },
      costAnalysis: {
        totalWeeklyBudgets: 0, // TODO: 实现成本分析
        totalPaidAmount: 0,
        totalUnpaidAmount: 0,
        budgetsByServiceType: []
      }
    };
  }

  // 用户管理方法

  // 创建或更新用户
  async upsertUser(data: {
    userid: string;
    unionid?: string;
    name: string;
    avatar?: string;
    stateCode?: string;
    managerUserid?: string;
    mobile?: string;
    hideMobile?: boolean;
    telephone?: string;
    jobNumber?: string;
    title?: string;
    email?: string;
    workPlace?: string;
    remark?: string;
    loginId?: string;
    exclusiveAccountType?: string;
    exclusiveAccount?: boolean;
    deptIdList?: number[];
    extension?: string;
    hiredDate?: Date;
    active?: boolean;
    realAuthed?: boolean;
    orgEmail?: string;
    orgEmailType?: string;
    senior?: boolean;
    admin?: boolean;
    boss?: boolean;
  }) {
    return this.prisma.user.upsert({
      where: { userid: data.userid },
      update: {
        ...data,
        lastSyncAt: new Date(),
      },
      create: {
        ...data,
        lastSyncAt: new Date(),
      },
    });
  }

  // 获取单个用户（包含角色信息）
  async getUser(userid: string) {
    return this.prisma.user.findUnique({
      where: { userid },
      include: {
        userRoles: {
          include: {
            role: {
              select: {
                id: true,
                name: true,
                displayName: true,
                description: true,
                isSystem: true,
                isActive: true
              }
            }
          },
          where: {
            OR: [
              { expiresAt: null },
              { expiresAt: { gt: new Date() } }
            ]
          }
        }
      }
    });
  }

  // 批量获取用户（包含角色信息）
  async getUsers(userids: string[]) {
    return this.prisma.user.findMany({
      where: {
        userid: {
          in: userids,
        },
      },
      include: {
        userRoles: {
          include: {
            role: {
              select: {
                id: true,
                name: true,
                displayName: true,
                description: true,
                isSystem: true,
                isActive: true
              }
            }
          },
          where: {
            OR: [
              { expiresAt: null },
              { expiresAt: { gt: new Date() } }
            ]
          }
        }
      }
    });
  }

  // 批量同步用户信息
  async syncUsers(users: Array<{
    userid: string;
    name: string;
    avatar?: string;
    mobile?: string;
    email?: string;
    department?: string[];
    position?: string;
    jobNumber?: string;
  }>) {
    const results = [];
    for (const user of users) {
      try {
        const result = await this.upsertUser(user);
        results.push(result);
      } catch (error) {
        console.error(`同步用户 ${user.userid} 失败:`, error);
      }
    }
    return results;
  }

  // 项目变更记录管理方法

  // 创建项目变更记录
  async createProjectChangeLog(data: {
    changeType: string;
    changeTitle: string;
    changeDetails?: any;
    beforeData?: any;
    afterData?: any;
    changedFields: string[];
    operatorId: string;
    operatorName: string;
    operatorIP?: string;
    userAgent?: string;
    reason?: string;
    description?: string;
    projectId: string;
  }) {
    return this.prisma.projectChangeLog.create({
      data: {
        changeType: data.changeType.toUpperCase() as any,
        changeTitle: data.changeTitle,
        changeDetails: data.changeDetails,
        beforeData: data.beforeData,
        afterData: data.afterData,
        changedFields: data.changedFields,
        operatorId: data.operatorId,
        operatorName: data.operatorName,
        operatorIP: data.operatorIP,
        userAgent: data.userAgent,
        reason: data.reason,
        description: data.description,
        projectId: data.projectId,
      },
      include: {
        project: {
          include: {
            brand: true
          }
        }
      }
    });
  }

  // 获取项目变更记录列表
  async getProjectChangeLogs(params: {
    projectId?: string;
    operatorId?: string;
    changeType?: string;
    startDate?: string;
    endDate?: string;
    page?: number;
    pageSize?: number;
    sortBy?: string;
    sortOrder?: string;
  }) {
    const {
      projectId,
      operatorId,
      changeType,
      startDate,
      endDate,
      page = 1,
      pageSize = 20,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = params;

    // 构建查询条件
    const where: any = {};

    if (projectId) {
      where.projectId = projectId;
    }

    if (operatorId) {
      where.operatorId = operatorId;
    }

    if (changeType) {
      where.changeType = changeType.toUpperCase();
    }

    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) {
        where.createdAt.gte = new Date(startDate);
      }
      if (endDate) {
        where.createdAt.lte = new Date(endDate);
      }
    }

    // 计算分页
    const skip = (page - 1) * pageSize;
    const take = pageSize;

    // 构建排序
    const orderBy: any = {};
    orderBy[sortBy] = sortOrder;

    // 执行查询
    const [changeLogs, total] = await Promise.all([
      this.prisma.projectChangeLog.findMany({
        where,
        skip,
        take,
        orderBy,
        include: {
          project: {
            include: {
              brand: true
            }
          }
        }
      }),
      this.prisma.projectChangeLog.count({ where })
    ]);

    return {
      changeLogs: changeLogs.map(log => this.transformProjectChangeLog(log)),
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    };
  }

  // 获取单个项目的变更记录
  async getProjectChangeLogsByProjectId(projectId: string, params?: {
    page?: number;
    pageSize?: number;
    sortOrder?: string;
  }) {
    const { page = 1, pageSize = 50, sortOrder = 'desc' } = params || {};

    const skip = (page - 1) * pageSize;
    const take = pageSize;

    const [changeLogs, total] = await Promise.all([
      this.prisma.projectChangeLog.findMany({
        where: { projectId },
        skip,
        take,
        orderBy: { createdAt: sortOrder as any },
        include: {
          project: {
            include: {
              brand: true
            }
          }
        }
      }),
      this.prisma.projectChangeLog.count({ where: { projectId } })
    ]);

    return {
      changeLogs: changeLogs.map(log => this.transformProjectChangeLog(log)),
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    };
  }

  // 转换项目变更记录数据
  private transformProjectChangeLog(log: any) {
    return {
      id: log.id,
      changeType: log.changeType,
      changeTitle: log.changeTitle,
      changeDetails: log.changeDetails,
      beforeData: log.beforeData,
      afterData: log.afterData,
      changedFields: log.changedFields,
      operatorId: log.operatorId,
      operatorName: log.operatorName,
      operatorIP: log.operatorIP,
      userAgent: log.userAgent,
      reason: log.reason,
      description: log.description,
      projectId: log.projectId,
      project: log.project ? {
        id: log.project.id,
        projectName: log.project.projectName,
        status: log.project.status,
        brand: log.project.brand ? {
          id: log.project.brand.id,
          name: log.project.brand.name
        } : undefined
      } : undefined,
      createdAt: log.createdAt
    };
  }

  // 获取需要同步的用户ID列表（本地不存在或超过同步时间的用户）
  async getUsersNeedSync(userids: string[], syncIntervalHours: number = 24): Promise<string[]> {
    const cutoffTime = new Date(Date.now() - syncIntervalHours * 60 * 60 * 1000);

    const existingUsers = await this.prisma.user.findMany({
      where: {
        userid: {
          in: userids,
        },
        OR: [
          { lastSyncAt: { lt: cutoffTime } },
          { isActive: false }
        ]
      },
      select: { userid: true }
    });

    const existingUserIds = existingUsers.map(u => u.userid);
    const missingUserIds = userids.filter(id => !existingUserIds.includes(id));

    // 返回缺失的用户ID和需要更新的用户ID
    return [...missingUserIds, ...existingUserIds];
  }

  // 标记用户为非活跃状态
  async deactivateUsers(userids: string[]) {
    return this.prisma.user.updateMany({
      where: {
        userid: {
          in: userids,
        },
      },
      data: {
        isActive: false,
        lastSyncAt: new Date(),
      },
    });
  }

  // 附件管理
  async createAttachment(data: {
    filename: string;
    originalName: string;
    size: number;
    mimeType: string;
    url: string;
    projectId: string | null;
    uploadedBy: string;
  }) {
    return this.prisma.attachment.create({
      data,
    });
  }

  async getAttachment(id: string) {
    return this.prisma.attachment.findUnique({
      where: { id },
    });
  }

  async deleteAttachment(id: string) {
    await this.prisma.attachment.delete({
      where: { id },
    });
    return true;
  }

  /**
   * 更新周预算的已付金额
   */
  async updateWeeklyBudgetPaidAmount(weeklyBudgetId: string, paidAmount: number) {
    await this.prisma.weeklyBudget.update({
      where: { id: weeklyBudgetId },
      data: {
        paidAmount: new Decimal(paidAmount),
        approvalStatus: 'APPROVED',
        approvalAmount: new Decimal(paidAmount),
        approvalReason: `CSV批量导入的已付款项，金额：${paidAmount}元`
      }
    });
  }

  async disconnect() {
    await this.prisma.$disconnect();
  }
}

export const db = new DatabaseService();
